# Production Environment Configuration
# Copy this file and customize for your production deployment
# Ensure all sensitive values are properly secured

NODE_ENV=production
PORT=3000
HOST=0.0.0.0
DATABASE_URL=**********************************/terrastories
JWT_SECRET=REPLACE_WITH_SECURE_32_PLUS_CHARACTER_SECRET
LOG_LEVEL=info

# Production-specific features (set by schema defaults)
# SYNC_ENABLED=true
# OFFLINE_MODE=false
# MEDIA_UPLOAD=true
# ADMIN_INTERFACE=true

# Additional production settings (if needed)
# SSL_ENABLED=true
# CORS_ORIGIN=https://your-domain.com