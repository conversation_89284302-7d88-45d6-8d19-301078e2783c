{"name": "terrastories-api", "version": "1.0.0", "description": "TypeScript backend for Terrastories geostorytelling platform", "main": "dist/server.js", "type": "module", "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "vitest", "test:coverage": "vitest run --coverage", "lint": "eslint .", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "tsx src/db/migrate.ts", "db:seed": "tsx src/db/seed.ts", "prepare": "husky", "validate": "npm run type-check && npm run lint && npm test"}, "dependencies": {"@fastify/cors": "^11.1.0", "@fastify/helmet": "^13.0.1", "@fastify/swagger": "^9.5.1", "@fastify/swagger-ui": "^5.2.3", "@types/pg": "^8.15.5", "better-sqlite3": "^12.2.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "drizzle-zod": "^0.8.3", "fastify": "^5.5.0", "pg": "^8.16.3", "postgres": "^3.4.7", "zod": "^4.0.17"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^24.3.0", "@vitest/coverage-v8": "^3.2.4", "drizzle-kit": "^0.31.4", "eslint": "^9.33.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "prettier": "^3.6.2", "tsx": "^4.20.4", "typescript": "^5.9.2", "typescript-eslint": "^8.39.1", "vitest": "^3.2.4"}, "keywords": ["terrastories", "typescript", "fastify", "drizzle", "geostorytelling"], "author": "Terrastories Team", "license": "MIT", "lint-staged": {"src/**/*.ts": ["eslint --fix", "prettier --write"], "tests/**/*.ts": ["eslint --fix", "prettier --write"], "*.{js,json,md}": ["prettier --write"]}}