Run the following tasks, and check them as done by adding a checkmark to the appropriate task. Add and commit only the appropriate files related to each change to the repo. All these should be parts of a new PR, chore/workflow-optimization branch.

- [ ] Optimize ROADMAP.md to reflect current status of Github issues and PRs and the new docs structure and the new docs/ISSUES_ROADMAP.md file.
- [ ] Update .claude/commands to always keep docs/GITHUB_ROADMAP_MAPPING.md up to date with the latest issues and PRs.
- [ ] Update .claude/commands to always keep docs/ISSUES_ROADMAP.md up to date with the latest issues and PRs.
- [ ] Check relevance of currently open issue #3 on Github in relation to new ISSUES_ROADMAP.md, update or close it in favor of new issues if neeeded.