# **6\. Target Database Schema (Drizzle ORM)**

This is the target schema for the TypeScript migration using Drizzle ORM. It is designed to be compatible with the existing PostgreSQL database.  
// schema.ts  
import {  
  pgTable,  
  serial,  
  text,  
  boolean,  
  timestamp,  
  integer,  
  decimal,  
  jsonb,  
} from 'drizzle-orm/pg-core';  
import { relations } from 'drizzle-orm';

// Core Tables

export const communities \= pgTable('communities', {  
  id: serial('id').primaryKey(),  
  name: text('name').notNull(),  
  description: text('description'),  
  slug: text('slug').unique().notNull(),  
  public\_stories: boolean('public\_stories').default(false),  
  theme\_id: integer('theme\_id').references(() \=\> themes.id),  
  created\_at: timestamp('created\_at').defaultNow(),  
  updated\_at: timestamp('updated\_at').defaultNow(),  
});

export const users \= pgTable('users', {  
  id: serial('id').primaryKey(),  
  email: text('email').unique().notNull(),  
  encrypted\_password: text('encrypted\_password').notNull(),  
  role: text('role').default('viewer').notNull(), // viewer, editor, admin, super\_admin  
  community\_id: integer('community\_id').references(() \=\> communities.id),  
  created\_at: timestamp('created\_at').defaultNow(),  
  updated\_at: timestamp('updated\_at').defaultNow(),  
});

export const stories \= pgTable('stories', {  
  id: serial('id').primaryKey(),  
  title: text('title').notNull(),  
  description: text('description'),  
  language: text('language'),  
  topic: text('topic'),  
  permission\_level: text('permission\_level').default('public'), // public, restricted, private  
  media\_urls: jsonb('media\_urls'), // To store paths to attached media  
  community\_id: integer('community\_id').references(() \=\> communities.id).notNull(),  
  created\_at: timestamp('created\_at').defaultNow(),  
  updated\_at: timestamp('updated\_at').defaultNow(),  
});

export const places \= pgTable('places', {  
  id: serial('id').primaryKey(),  
  name: text('name').notNull(),  
  description: text('description'),  
  type\_of\_place: text('type\_of\_place'),  
  region: text('region'),  
  lat: decimal('lat', { precision: 10, scale: 6 }),  
  long: decimal('long', { precision: 10, scale: 6 }),  
  photo\_url: text('photo\_url'),  
  name\_audio\_url: text('name\_audio\_url'),  
  community\_id: integer('community\_id').references(() \=\> communities.id).notNull(),  
  created\_at: timestamp('created\_at').defaultNow(),  
  updated\_at: timestamp('updated\_at').defaultNow(),  
});

export const speakers \= pgTable('speakers', {  
  id: serial('id').primaryKey(),  
  name: text('name').notNull(),  
  photo\_url: text('photo\_url'),  
  community\_id: integer('community\_id').references(() \=\> communities.id).notNull(),  
  created\_at: timestamp('created\_at').defaultNow(),  
  updated\_at: timestamp('updated\_at').defaultNow(),  
});

export const themes \= pgTable('themes', {  
    id: serial('id').primaryKey(),  
    name: text('name').notNull(),  
});

// Join Tables for Many-to-Many Relationships

export const story\_places \= pgTable('story\_places', {  
    story\_id: integer('story\_id').references(() \=\> stories.id).notNull(),  
    place\_id: integer('place\_id').references(() \=\> places.id).notNull(),  
});

export const story\_speakers \= pgTable('story\_speakers', {  
    story\_id: integer('story\_id').references(() \=\> stories.id).notNull(),  
    speaker\_id: integer('speaker\_id').references(() \=\> speakers.id).notNull(),  
});

// Drizzle Relations

export const communityRelations \= relations(communities, ({ many }) \=\> ({  
    stories: many(stories),  
    places: many(places),  
    speakers: many(speakers),  
    users: many(users),  
}));

export const storyRelations \= relations(stories, ({ one, many }) \=\> ({  
    community: one(communities, { fields: \[stories.community\_id\], references: \[communities.id\] }),  
    story\_places: many(story\_places),  
    story\_speakers: many(story\_speakers),  
}));

export const placeRelations \= relations(places, ({ one, many }) \=\> ({  
    community: one(communities, { fields: \[places.community\_id\], references: \[communities.id\] }),  
    story\_places: many(story\_places),  
}));

export const speakerRelations \= relations(speakers, ({ one, many }) \=\> ({  
    community: one(communities, { fields: \[speakers.community\_id\], references: \[communities.id\] }),  
    story\_speakers: many(story\_speakers),  
}));  
