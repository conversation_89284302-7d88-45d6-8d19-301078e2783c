# **3\. API Endpoints**

The API is divided into three main namespaces, based on access level.

## **3.1. Public API (/api)**

This is a **read-only JSON API** that does not require authentication. All resources are nested under a community.

* GET /api/communities \- Lists all communities.  
* GET /api/communities/:id \- Shows a single community.  
* GET /api/communities/:community\_id/stories \- Lists all public stories for a community.  
* GET /api/communities/:community\_id/stories/:id \- Shows a single public story.  
* GET /api/communities/:community\_id/places/:id \- Shows a single place.

## **3.2. Member Dashboard API (/member)**

These endpoints require authentication and are for community members to manage content. They provide full CRUD functionality.

* **Stories**:  
  * GET /member/stories  
  * POST /member/stories  
  * GET /member/stories/:id  
  * PUT/PATCH /member/stories/:id  
  * DELETE /member/stories/:id  
* **Places**:  
  * GET /member/places  
  * POST /member/places  
  * PUT/PATCH /member/places/:id  
  * DELETE /member/places/:id  
* **Speakers**:  
  * GET /member/speakers  
  * POST /member/speakers  
  * PUT/PATCH /member/speakers/:id  
  * DELETE /member/speakers/:id

## **3.3. Super Admin API (/super\_admin)**

These endpoints are for super administrators to manage communities and users across the entire application.

* **Communities**:  
  * GET /super\_admin/communities  
  * POST /super\_admin/communities  
  * PUT/PATCH /super\_admin/communities/:id  
* **Users**:  
  * GET /super\_admin/users  
  * POST /super\_admin/users  
  * PUT/PATCH /super\_admin/users/:id  
* **Themes**:  
  * GET /super\_admin/themes  
  * POST /super\_admin/themes