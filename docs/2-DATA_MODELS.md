# **2\. Core Data Models**

Terrastories is built around five core models that have intricate relationships.

### **1\. Community**

The central organizing entity and the basis for multi-tenancy. All other core data belongs to a Community.

* **Description**: Represents an Indigenous or local community. It controls data sovereignty boundaries, access, and customization (themes, map styles).  
* **Key Fields**: name, slug, description, public\_stories.  
* **Relationships**: has\_many Stories, Places, Speakers, Users, and Themes.

### **2\. Story**

The primary content entity, representing oral histories and narratives.

* **Description**: Contains the narrative content, which can be linked to multiple places and speakers.  
* **Key Fields**: title, description, language, topic, permission\_level.  
* **Relationships**:  
  * belongs\_to a Community.  
  * has\_and\_belongs\_to\_many Places.  
  * has\_and\_belongs\_to\_many Speakers.  
  * has\_many\_attached media files (via ActiveStorage in Rails).

### **3\. Place**

A geographic location that is part of a story.

* **Description**: Represents a point or region on the map. Places can be associated with multiple stories.  
* **Key Fields**: name, description, type\_of\_place, region, lat, long.  
* **Relationships**:  
  * belongs\_to a Community.  
  * has\_and\_belongs\_to\_many Stories.  
  * has\_one\_attached photo.  
  * has\_one\_attached name\_audio.

### **4\. Speaker**

The storyteller or source of a story.

* **Description**: Represents the person who narrated a story.  
* **Key Fields**: name, birthdate, photo.  
* **Relationships**:  
  * belongs\_to a Community.  
  * has\_and\_belongs\_to\_many Stories.

### **5\. User**

A registered user who can access and manage content within a community.

* **Description**: Users are scoped to a single community and have different roles (e.g., admin, editor, viewer).  
* **Key Fields**: email, role.  
* **Relationships**: belongs\_to a Community.