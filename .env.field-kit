# Field Kit Environment Configuration
# Optimized for offline deployment via WiFi hotspot
# Designed for community data collection in remote areas

NODE_ENV=field-kit
PORT=3000
HOST=0.0.0.0
DATABASE_URL=./field-kit-data.db
JWT_SECRET=field-kit-deployment-secret-change-me
LOG_LEVEL=info

# Field Kit specific features (set by schema defaults)
# OFFLINE_MODE=true
# SYNC_ENABLED=false (no internet connection expected)
# MEDIA_UPLOAD=true (essential for community storytelling)
# ADMIN_INTERFACE=true (for community coordinators)

# Field Kit specific settings
# WIFI_HOTSPOT_NAME=Terrastories-FieldKit
# LOCAL_NETWORK_ONLY=true
# BACKUP_ENABLED=true