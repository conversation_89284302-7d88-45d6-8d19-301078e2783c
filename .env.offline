# Offline Environment Configuration
# Minimal configuration for completely isolated operation
# Suitable for sensitive data environments or air-gapped systems

NODE_ENV=offline
PORT=3000
HOST=127.0.0.1
DATABASE_URL=:memory:
JWT_SECRET=offline-mode-secret-temporary-session
LOG_LEVEL=error

# Offline specific features (set by schema defaults)
# OFFLINE_MODE=true
# SYNC_ENABLED=false (no network connectivity)
# MEDIA_UPLOAD=false (minimal features)
# ADMIN_INTERFACE=false (reduced attack surface)

# Offline specific settings
# NETWORK_DISABLED=true
# EXTERNAL_REQUESTS_BLOCKED=true
# DATA_PERSISTENCE=memory-only