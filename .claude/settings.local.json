{"permissions": {"allow": ["mcp__serena__activate_project", "Bash(find:*)", "mcp__serena__find_file", "mcp__serena__search_for_pattern", "mcp__serena__list_dir", "<PERSON><PERSON>(cat:*)", "Bash(gh issue list:*)", "<PERSON><PERSON>(gh issue view:*)", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "<PERSON><PERSON>(mkdir:*)", "Bash(npm install)", "Bash(npm view:*)", "Bash(npm test)", "Bash(npm run type-check:*)", "Bash(npm run lint)", "Bash(npm run build:*)", "Bash(npm run:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(npx husky init:*)", "Bash(git push:*)", "Bash(git checkout:*)", "Bash(npm test:*)", "Bash(node:*)", "Bash(npx npm-check-updates:*)", "Bash(npm outdated)", "<PERSON><PERSON>(gh run list:*)", "Bash(npm audit:*)", "Bash(gh pr list:*)", "Bash(gh pr view:*)", "Bash(gh pr checks:*)", "Bash(gh pr diff:*)", "Bash(npm update:*)", "Bash(git commit:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__serena__check_onboarding_performed", "Bash(NODE_ENV=development JWT_SECRET=test npm test tests/config.test.ts -t \"should detect development environment by default\")", "<PERSON><PERSON>(mv:*)", "Bash(npm install:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)"], "deny": [], "defaultMode": "acceptEdits"}}